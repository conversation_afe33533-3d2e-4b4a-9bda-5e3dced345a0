import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest.dart' as tz;

import 'core/constants/app_constants.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/notification_service.dart';
import 'presentation/providers/router_provider.dart';
import 'data/models/user_model.dart';
import 'data/models/water_log_model.dart';

/// Global instance of FlutterLocalNotificationsPlugin
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp();
  
  // Initialize Hive
  await Hive.initFlutter();
  
  // Register Hive adapters
  Hive.registerAdapter(UserModelAdapter());
  Hive.registerAdapter(WaterLogModelAdapter());
  
  // Open Hive boxes
  await Hive.openBox<UserModel>(AppConstants.userBoxName);
  await Hive.openBox<WaterLogModel>(AppConstants.waterLogBoxName);
  await Hive.openBox(AppConstants.settingsBoxName);
  
  // Initialize timezone data
  tz.initializeTimeZones();
  
  // Initialize notifications
  await NotificationService.initialize();
  
  runApp(
    ProviderScope(
      child: WaterReminderApp(),
    ),
  );
}

class WaterReminderApp extends ConsumerWidget {
  const WaterReminderApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    
    return MaterialApp.router(
      title: 'Water Reminder',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: router,
    );
  }
}
