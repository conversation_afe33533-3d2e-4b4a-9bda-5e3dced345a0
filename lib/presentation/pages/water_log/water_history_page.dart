import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Water history page showing past water intake logs
class WaterHistoryPage extends ConsumerStatefulWidget {
  const WaterHistoryPage({super.key});

  @override
  ConsumerState<WaterHistoryPage> createState() => _WaterHistoryPageState();
}

class _WaterHistoryPageState extends ConsumerState<WaterHistoryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Water History'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Today'),
            Tab(text: 'This Week'),
            Tab(text: 'This Month'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_month),
            onPressed: () {
              // TODO: Show date picker
            },
            tooltip: 'Select Date',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTodayTab(),
          _buildWeekTab(),
          _buildMonthTab(),
        ],
      ),
    );
  }

  Widget _buildTodayTab() {
    // TODO: Replace with actual data
    final todayLogs = [
      _MockWaterLog(
        amount: 250,
        time: '2:30 PM',
        note: 'After lunch',
        source: 'Manual Entry',
      ),
      _MockWaterLog(
        amount: 500,
        time: '12:15 PM',
        note: null,
        source: 'Reminder',
      ),
      _MockWaterLog(
        amount: 300,
        time: '10:00 AM',
        note: 'Morning hydration',
        source: 'Quick Add',
      ),
      _MockWaterLog(
        amount: 200,
        time: '8:30 AM',
        note: null,
        source: 'Manual Entry',
      ),
    ];

    return _buildLogsList(todayLogs, showDate: false);
  }

  Widget _buildWeekTab() {
    // TODO: Replace with actual data
    final weekLogs = [
      _MockWaterLog(
        amount: 1800,
        time: 'Today',
        note: '90% of goal achieved',
        source: 'Daily Summary',
      ),
      _MockWaterLog(
        amount: 2200,
        time: 'Yesterday',
        note: 'Goal exceeded!',
        source: 'Daily Summary',
      ),
      _MockWaterLog(
        amount: 1600,
        time: '2 days ago',
        note: '80% of goal',
        source: 'Daily Summary',
      ),
    ];

    return _buildLogsList(weekLogs, showDate: true);
  }

  Widget _buildMonthTab() {
    return _buildMonthlyView();
  }

  Widget _buildLogsList(List<_MockWaterLog> logs, {required bool showDate}) {
    if (logs.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: logs.length,
      itemBuilder: (context, index) {
        final log = logs[index];
        return _buildLogItem(log, showDate: showDate);
      },
    );
  }

  Widget _buildLogItem(_MockWaterLog log, {required bool showDate}) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.water_drop,
                color: theme.colorScheme.primary,
                size: 16,
              ),
              Text(
                '${log.amount}',
                style: theme.textTheme.labelSmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        title: Text(
          '${log.amount}ml',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              showDate ? log.time : '${log.time} • ${log.source}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            if (log.note != null) ...[
              const SizedBox(height: 2),
              Text(
                log.note!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
        trailing: showDate
            ? null
            : PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'edit') {
                    // TODO: Edit log
                  } else if (value == 'delete') {
                    // TODO: Delete log
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Text('Edit'),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Text('Delete'),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.water_drop_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withOpacity(0.4),
          ),
          const SizedBox(height: 16),
          Text(
            'No water logs yet',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start tracking your water intake!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyView() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Monthly summary card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'December 2024',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildSummaryItem(
                          'Total Intake',
                          '45.2L',
                          Icons.water_drop,
                          Colors.blue,
                        ),
                      ),
                      Expanded(
                        child: _buildSummaryItem(
                          'Daily Average',
                          '1.5L',
                          Icons.trending_up,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildSummaryItem(
                          'Goals Met',
                          '18/30',
                          Icons.flag,
                          Colors.orange,
                        ),
                      ),
                      Expanded(
                        child: _buildSummaryItem(
                          'Best Streak',
                          '7 days',
                          Icons.local_fire_department,
                          Colors.red,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Calendar view placeholder
          Text(
            'Calendar View',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Card(
            child: Container(
              height: 200,
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.calendar_month,
                      size: 48,
                      color: theme.colorScheme.primary.withOpacity(0.5),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Calendar View Coming Soon',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class _MockWaterLog {
  final int amount;
  final String time;
  final String? note;
  final String source;

  _MockWaterLog({
    required this.amount,
    required this.time,
    this.note,
    required this.source,
  });
}
